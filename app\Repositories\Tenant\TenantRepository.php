<?php
namespace App\Repositories\Tenant;

use App\Repositories\EloquentRepository;
use App\Enums\TenantStatus;

class TenantRepository extends EloquentRepository implements TenantRepositoryInterface
{
	public function getModel()
	{
		return \App\Models\Tenant::class;
	}

	public function getAllTenants($dataSearch)
	{
		// Chuyển đổi tab thành enum TenantStatus
		$tabStatus = isset($dataSearch['tab']) && !empty($dataSearch['tab']) 
			? TenantStatus::fromString($dataSearch['tab']) 
			: TenantStatus::ALL;

        $page = $dataSearch['page'] ?? null;
        $perPage = $dataSearch['perPage'] ?? null;
		
		$select_columns = [
			'id',
			'name',	
			'domain_name',
			'start_date',
			'end_date',
			'is_active',
			'create_by',
            'created_at',
		];

		$query = $this->model
			->select($select_columns)
			->with(['createBy:id,full_name']);

		// Tạo baseQuery để sử dụng cho việc đếm các tab
        $baseQuery = clone $query;

        // Áp dụng điều kiện theo tab
        $tabStatus->applyToQuery($query);
        
        $orderBy = $dataSearch['orderBy'] ?? 'created_at';
        $orderDirection = $dataSearch['orderDirection'] ?? 'desc';
        $query->orderBy($orderBy, $orderDirection);
        
        // Lấy danh sách trạng thái từ enum
        $statusList = TenantStatus::cases();
        $counts = [];
        foreach ($statusList as $status) {
            $countQuery = clone $baseQuery;
            $status->applyToQuery($countQuery);
            $counts[strtolower($status->name)] = $countQuery->count();
        }

        if ($page && $perPage) {
            $tenants = $query->paginate($perPage, ['*'], 'page', $page);
        } else {
            $tenants = $query->get();
        }
        
        return [
            'tenants' => $tenants,
            'counts' => $counts
        ];
	}

	public function findByDomainName($domain_name)
	{
		return $this->model->where('domain_name', $domain_name)->first();
	}
}
?>
