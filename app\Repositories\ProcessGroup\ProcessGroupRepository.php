<?php
namespace App\Repositories\ProcessGroup;

use App\Repositories\EloquentRepository;
use App\Enums\ProcessGroupStatus;

class ProcessGroupRepository extends EloquentRepository implements ProcessGroupRepositoryInterface
{
	public function getModel()
	{
		return \App\Models\ProcessGroup::class;
	}

	public function getProcessGroupByScopeUses($search)
	{
		$select_columns = [
			'id',
			'name',
			'is_active',
		];

		$process_groups = $this->model
			->select($select_columns)
			->when(($search), function($query) use ($search) {
				$query->where('name', 'LIKE', '%' . $search . '%');
			})
			->where('is_active', ProcessGroupStatus::True->value)
			->limit(10)
			->get();
			
		return $process_groups;	
	}
}
?>