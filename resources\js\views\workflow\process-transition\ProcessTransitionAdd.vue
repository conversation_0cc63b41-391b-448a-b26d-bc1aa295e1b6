<template>
    <CCol :xs="12">
        <BTabs no-body content-class="mt-3" v-model="state.tabIndex">
            <BTab :title="$t('workflow.process_transition.create')" active>
                <Form ref="form" @submit="handleSubmitFormProcessTransition" :validation-schema="schemaCreate()">
                    <CCol :xs="12" class="mb-3">
                        <label class="mb-1">
                            {{ $t('workflow.process_transition.name') }}
                            <span class="text-danger">*</span>
                        </label>
                        <Field 
                            v-model="dataProcessTransition.name"
                            name="name" 
                            type="text" 
                            class="form-control" 
                            maxlength="250" 
                            @change="handleInputNameAdd(dataProcessTransition.name)"
                        />
                        <ErrorMessage
                            as="div"
                            name="name"
                            class="text-danger"
                        />
                    </CCol>
                    <CCol :xs="12" class="mb-3">
                        <label class="mb-1">
                            {{ $t('workflow.process_transition.slug') }}
                            <span class="text-danger">*</span>
                        </label>
                        <Field 
                            v-model="dataProcessTransition.slug"
                            name="slug" 
                            type="text" 
                            class="form-control" 
                            maxlength="200" 
                            :readonly="true"
                        />
                        <ErrorMessage
                            as="div"
                            name="slug"
                            class="text-danger"
                        />
                        <div class="text-danger mb-3" v-if="checkDuplicateSlugAdd(dataProcessTransition.slug)">
                            {{ $t('workflow.process_transition.validate.duplicate_slug') }}
                        </div>
                    </CCol>
                    <CCol :xs="12" class="mb-3">
                        <label class="mb-1">
                            {{ $t('workflow.process_transition.apply_process') }}
                            <span class="text-danger">*</span>
                        </label>
                        <Field 
                            name="apply_process"
                            v-slot="{ field }"
                        >
                            <Multiselect
                                v-bind="field"
                                :placeholder="$t('workflow.choose')"
                                v-model="dataProcessTransition.to_process_id"
                                :close-on-select="false"
                                :filter-results="false"
                                :resolve-on-load="false"
                                :infinite="true"
                                :limit="10"
                                :clear-on-search="true"
                                :searchable="true"
                                :delay="0"
                                :min-chars="0"
                                :object="true"
                                :options="async (query) => {
                                    return await debouncedGetOptionWorkflows(query)
                                }"
                                @open="debouncedGetOptionWorkflows('')"
                                :can-clear="false"
                            />
                        </Field>
                        <ErrorMessage
                            as="div"
                            name="apply_process"
                            class="text-danger"
                        />
                    </CCol>
                    <CCol :xs="12" class="mb-3">
                        <div class="form-check form-check-inline">
                            <input
                                v-model="dataProcessTransition.type_create"
                                name="type_create" 
                                type="radio" 
                                value="auto"
                                class="form-check-input p-2" 
                            />
                            <label class="form-check-label ms-1">
                                {{ $t('workflow.process_transition.type_create_auto') }}
                            </label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input
                                v-model="dataProcessTransition.type_create"
                                name="type_create" 
                                type="radio" 
                                value="manual"
                                class="form-check-input p-2" 
                            />
                            <label class="form-check-label ms-1">
                                {{ $t('workflow.process_transition.type_create_manual') }}
                            </label>
                        </div>
                    </CCol>
                    <CCardFooter>
                        <div class="d-flex justify-content-end">
                            <CButton 
                                type="button"
                                class="btn btn-light border m-1"
                                @click="closeFormProcessTransition"
                            >
                                <span class="text-uppercase">
                                    {{ $t('workflow.process_transition.close') }}
                                </span>
                            </CButton>
                            <CButton 
                                type="submit"
                                class="btn btn-primary m-1"
                            >
                                <span class="text-uppercase">
                                    {{ $t('workflow.stage.save_update') }}
                                </span>
                            </CButton>
                        </div>
                    </CCardFooter>
                </Form>
            </BTab>
            <BTab :title="$t('workflow.process_transition.list')">
                <CTable align="middle" responsive>
                    <table class="table table-hover">
                        <tbody v-if="listDataProcessTransitions.length > 0">
                            <tr 
                                v-for="(processTransition, index) in listDataProcessTransitions" 
                                :key="index"
                            >
                                <td class="align-middle">{{ processTransition.value.name }}</td> 
                                <td class="align-middle col-sm-1 table__td--action">
                                    <svg @click="editProcessTransition(index, processTransition.value)"  class="me-2" xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 0 24 24" width="18px" fill="#83868C">
                                        <path d="M0 0h24v24H0V0z" fill="none"/>
                                        <path d="M14.06 9.02l.92.92L5.92 19H5v-.92l9.06-9.06M17.66 3c-.25 0-.51.1-.7.29l-1.83 1.83 3.75 3.75 1.83-1.83c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.2-.2-.45-.29-.71-.29zm-3.6 3.19L3 17.25V21h3.75L17.81 9.94l-3.75-3.75z"/>
                                    </svg>    
                                    <svg @click="removeProcessTransition(index)" class="me-2" xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 0 24 24" width="18px" fill="#83868C">
                                        <path d="M0 0h24v24H0V0z" fill="none"/>
                                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/>
                                    </svg>  
                                </td>
                            </tr>
                        </tbody>
                        <tbody v-else>
                            <tr>
                                <td colspan="2" class="align-middle text-center">
                                    {{ $t('search.no_matching_records_found') }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </CTable>
                <BAccordion v-if="state.activeTabEdit">
                    <BAccordionItem :title="$t('workflow.process_transition.edit')" visible>
                        <Form ref="form" @submit="handleSubmitEditProcessTransition" :validation-schema="schemaEdit()">
                            <CCol :xs="12" class="mb-3">
                                <label class="mb-1">
                                    {{ $t('workflow.process_transition.name') }}
                                    <span class="text-danger">*</span>
                                </label>
                                <Field 
                                    v-model="state.processTransitionDetail.name"
                                    name="name" 
                                    type="text" 
                                    class="form-control" 
                                    maxlength="250" 
                                    @change="handleInputNameEdit(state.processTransitionDetail.name)"
                                />
                                <ErrorMessage
                                    as="div"
                                    name="name"
                                    class="text-danger"
                                />
                            </CCol>
                            <CCol :xs="12" class="mb-3">
                                <label class="mb-1">
                                    {{ $t('workflow.process_transition.slug') }}
                                    <span class="text-danger">*</span>
                                </label>
                                <Field 
                                    v-model="state.processTransitionDetail.slug"
                                    name="slug" 
                                    type="text" 
                                    class="form-control" 
                                    maxlength="200" 
                                    :readonly="true"
                                />
                                <ErrorMessage
                                    as="div"
                                    name="slug"
                                    class="text-danger"
                                />
                                <div class="text-danger mb-3" v-if="checkDuplicateSlugEdit(state.processTransitionDetail.slug, state.indexEdit)">
                                    {{ $t('workflow.process_transition.validate.duplicate_slug') }}
                                </div>
                            </CCol>
                            <CCol :xs="12" class="mb-3">
                                <label class="mb-1">
                                    {{ $t('workflow.process_transition.apply_process') }}
                                    <span class="text-danger">*</span>
                                </label>
                                <Multiselect
                                    :placeholder="$t('workflow.choose')"
                                    v-model="state.processTransitionDetail.to_process_id"
                                    :close-on-select="false"
                                    :filter-results="false"
                                    :resolve-on-load="false"
                                    :infinite="true"
                                    :limit="10"
                                    :clear-on-search="true"
                                    :searchable="true"
                                    :delay="0"
                                    :min-chars="0"
                                    :object="true"
                                    :options="async (query) => {
                                        return await debouncedGetOptionWorkflows(query)
                                    }"
                                    @open="debouncedGetOptionWorkflows('')"
                                    :can-clear="false"
                                />
                            </CCol>
                            <CCol :xs="12" class="mb-3">
                                <div class="form-check form-check-inline">
                                    <input
                                        v-model="state.processTransitionDetail.type_create"
                                        name="type_create" 
                                        type="radio" 
                                        value="auto"
                                        class="form-check-input p-2" 
                                    />
                                    <label class="form-check-label ms-1">
                                        {{ $t('workflow.process_transition.type_create_auto') }}
                                    </label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input
                                        v-model="state.processTransitionDetail.type_create"
                                        name="type_create" 
                                        type="radio" 
                                        value="manual"
                                        class="form-check-input p-2" 
                                    />
                                    <label class="form-check-label ms-1">
                                        {{ $t('workflow.process_transition.type_create_manual') }}
                                    </label>
                                </div>
                            </CCol>
                            <CCardFooter>
                                <div class="d-flex justify-content-end">
                                    <CButton 
                                        type="button"
                                        class="btn btn-light border m-1"
                                        @click="closeEditProcessTransition"
                                    >
                                        <span class="text-uppercase">
                                            {{ $t('workflow.process_transition.close') }}
                                        </span>
                                    </CButton>
                                    <CButton 
                                        type="submit"
                                        class="btn btn-primary m-1"
                                    >
                                        <span class="text-uppercase">
                                            {{ $t('workflow.stage.save_update') }}
                                        </span>
                                    </CButton>
                                </div>
                            </CCardFooter>
                        </Form>
                    </BAccordionItem>
                </BAccordion>
            </BTab>
        </BTabs>
    </CCol>
</template>

<script lang="ts">
import { defineComponent, reactive } from 'vue'
import { useToast } from 'vue-toast-notification';
import { useI18n } from "vue-i18n";
import Multiselect from '@vueform/multiselect';
import { Form, Field, ErrorMessage } from 'vee-validate';
import * as yup from 'yup';
import cloneDeep from 'lodash/cloneDeep'
import useOptions from '@/composables/option';
import  { generateStandardSlug } from "@/utils/utils";
import debounce from 'lodash.debounce';

export default defineComponent({
    name: 'ProcessTransitionAdd',
    emits: ['close-modal-process-transition', 'reset-modal-process-transition', 'add-process-transition', 'edit-process-transition', 'remove-process-transition'],

    components: {
        Multiselect,
        Form,
		Field,
		ErrorMessage,
    },

    props: {
        dataProcessTransition: {
            type: Object,
            default: {},
            required: true,
        },
        listDataProcessTransitions: {
            type: Array as () => Array<any>, 
            required: true,
            default: () => []
        },
    },

    setup(props: any, {emit}) {
        const { t }  = useI18n();
        const $toast = useToast();

        const schemaCreate = () => {
            let schemaForm = yup.object().shape({});

            schemaForm = schemaForm.shape({
                name: yup.string()
                    .required(`${t('workflow.process_transition.name')} ${t('workflow.process_transition.validate.required')}`),
                slug: yup.string()
                    .required(`${t('workflow.process_transition.slug')} ${t('workflow.process_transition.validate.required')}`),
                apply_process: yup.object()
                    .required(`${t('workflow.process_transition.apply_process')} ${t('workflow.process_transition.validate.required')}`)
                    .typeError(`${t('workflow.process_transition.apply_process')} ${t('workflow.process_transition.validate.required')}`),
            });

            return schemaForm;
        }

        const schemaEdit = () => {
            let schemaForm = yup.object().shape({});

            schemaForm = schemaForm.shape({
                name: yup.string()
                    .required(`${t('workflow.process_transition.name')} ${t('workflow.process_transition.validate.required')}`),
                slug: yup.string()
                    .required(`${t('workflow.process_transition.slug')} ${t('workflow.process_transition.validate.required')}`),
            });

            return schemaForm;
        }

        const state = reactive({
            tabIndex: 0,
            activeTabEdit: false,
            indexEdit: null as any,
            processTransitionDetail: {} as any,
            selectOptionOptionWorkflows: [] as Array<any>,
		});

        const closeFormProcessTransition = () => {
            emit('close-modal-process-transition');
		}

        const handleSubmitFormProcessTransition = async () => {
            if (!checkDuplicateSlugAdd(props.dataProcessTransition.slug)) {
                emit("add-process-transition", props.dataProcessTransition);
                emit('reset-modal-process-transition');
                state.tabIndex = 1;
                $toast.open({
                    message: t('toast.status.ACTION_SUCCESS'),
                    type: "success",
                    duration: 5000,
                    dismissible: true,
                    position: "bottom-right",
                });
            }
        }

        const editProcessTransition = async (index: number, processTransition: object): Promise<void> => {
            state.indexEdit = index;
            // Check if processTransition has 'id' key to determine if it needs formatting
            if ('id' in processTransition) {
                // Load options first if not already loaded
                let result = await getWorkflows('');
                state.selectOptionOptionWorkflows = Object.values(result).map((elem: any) => ({
                    value: elem.id,
                    label: elem.name,
                    formId: elem.process_version_active?.form_id,
                }));
                // Format API data to form data structure
                state.processTransitionDetail = formatApiDataToFormData(processTransition);
            } else {
                // Use existing form data structure
                state.processTransitionDetail = cloneDeep(processTransition);
            }
            state.activeTabEdit = true;
		}

        const formatApiDataToFormData = (apiData: any) => {
            if (apiData.formatted === undefined) {
                // Find to_process_id option from selectOptionOptionWorkflows
                // The correct field name is 'to_process_id' based on the ProcessTransition model
                let applyProcessOption = null;
                const processId = apiData.to_process_id;
                if (processId && state.selectOptionOptionWorkflows.length > 0) {
                    applyProcessOption = state.selectOptionOptionWorkflows.find((option: any) => {
                        return option.value === processId;
                    });
                }
                
                return {
                    id: apiData.id,
                    name: apiData.name,
                    slug: apiData.slug,
                    to_process_id: applyProcessOption || null,
                    type_create: apiData.type_create || 'auto',
                    formatted: true,
                };
            }
            
            return apiData;
        }

        const removeProcessTransition = (index: number): void => {
            const slugValue = props.listDataProcessTransitions[index].value.slug;
			props.listDataProcessTransitions.splice(index, 1);
            emit("remove-process-transition", props.listDataProcessTransitions, slugValue);
            closeEditProcessTransition();
		}

        const closeEditProcessTransition = () => {
            state.activeTabEdit = false;
        }

        const handleSubmitEditProcessTransition = async () => {
            if (!checkDuplicateSlugEdit(state.processTransitionDetail.slug, state.indexEdit)) {
                const slugValue = props.listDataProcessTransitions[state.indexEdit].value.slug;
                emit("edit-process-transition", state.processTransitionDetail, state.indexEdit, slugValue);
                closeEditProcessTransition();
                $toast.open({
                    message: t('toast.status.ACTION_SUCCESS'),
                    type: "success",
                    duration: 5000,
                    dismissible: true,
                    position: "bottom-right",
                });
            }
        }

        const { getWorkflows } = useOptions();

        const getOptionWorkflows = async (query: string) => {
			try {
                let result = await getWorkflows(query);
                if (Array.isArray(result) && result.length > 0) {
                    const mappedOptions = result.map((elem: any) => ({
                        value: elem.id,
                        label: elem.name,
                        formId: elem.process_version_active?.form_id,
                    }));
                    state.selectOptionOptionWorkflows = mappedOptions;
                    return mappedOptions;
                }

                return [];
            } catch (error) {
                return [];
            }
		}

        const debouncedGetOptionWorkflows = debounce(getOptionWorkflows, 500);

        const handleInputNameAdd = async (valueName: any) => {
            if (!valueName) {
                props.dataProcessTransition.slug = '';
			} else {
    			props.dataProcessTransition.slug = generateStandardSlug(valueName);
			}
		}

        const handleInputNameEdit = async (valueName: any) => {
            if (!valueName) {
                state.processTransitionDetail.slug = '';
			} else {
    			state.processTransitionDetail.slug = generateStandardSlug(valueName);
			}
		}

        const checkDuplicateSlugAdd = (slug: string): boolean => {
            return props.listDataProcessTransitions.some((item: any) => item.value.slug === slug);
        }

        const checkDuplicateSlugEdit = (slug: string, indexEdit: number): boolean => {
            return props.listDataProcessTransitions.some((item: any, index: number) => {
                // Bỏ qua kiểm tra nếu là chính slug hiện tại (indexEdit)
                if (index === indexEdit) {
                    return false;
                }
                return item.value.slug === slug;
            });
        }

        return {
            state,
            schemaCreate,
            schemaEdit,
            debouncedGetOptionWorkflows,
            closeFormProcessTransition,
            handleSubmitFormProcessTransition,
            editProcessTransition,
            formatApiDataToFormData,
            removeProcessTransition,
            closeEditProcessTransition,
            handleSubmitEditProcessTransition,
            handleInputNameAdd,
            handleInputNameEdit,
            checkDuplicateSlugAdd,
            checkDuplicateSlugEdit
        }
    },
});
</script>
<style type="text/css" scoped>
svg, .cursor-pointer {
    cursor: pointer;
}
.table__td--action {
    min-width: 70px !important;
}
</style>
<style src="@vueform/multiselect/themes/default.css"></style>