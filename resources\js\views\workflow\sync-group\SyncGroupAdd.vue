<template>
    <CCol :xs="12">
        <BTabs no-body content-class="mt-3" v-model="state.tabIndex">
            <BTab :title="$t('workflow.sync_group.create')" active>
                <FormKit
                    ref="formSyncGroup"
                    type="form"
                    :actions="false"
                    incomplete-message=" "
                    @submit="handleSubmitFormSyncGroup"
                >
                    <FormKit
                        label-class="required-label" 
                        type="text"
                        :label="$t('workflow.sync_group.name')"
                        :floating-label="false"
                        v-model="dataSyncGroup.name"
                        validation="required|length:1,200"
                        @input="(value) => handleInputNameAdd(value)"
                        :validation-messages="{
                            required: `${$t('workflow.sync_group.name')} ${$t('workflow.sync_group.validate.required')}`,
                            length: `${$t('workflow.sync_group.name')} ${$t('workflow.sync_group.validate.name_length')}`
                        }"
                    />
                    <FormKit
                        label-class="required-label" 
                        type="text"
                        :label="$t('workflow.sync_group.slug')"
                        :floating-label="false"
                        v-model="dataSyncGroup.slug"
                        validation="required|length:1,100"
                        :readonly="true"
                        :validation-messages="{
                            required: `${$t('workflow.sync_group.slug')} ${$t('workflow.sync_group.validate.required')}`,
                            length: `${$t('workflow.sync_group.slug')} ${$t('workflow.sync_group.validate.name_length')}`
                        }"
                    />
                    <div class="text-danger mb-3" v-if="checkDuplicateSlugAdd(dataSyncGroup.slug)">
                        {{ $t('workflow.sync_group.validate.duplicate_slug') }}
                    </div>
                </FormKit>
                <CCardFooter>
                    <div class="d-flex justify-content-end">
                        <CButton 
                            type="button"
                            class="btn btn-light border m-1"
                            @click="closeFormSyncGroup"
                        >
                            <span class="text-uppercase">
                                {{ $t('workflow.sync_group.close') }}
                            </span>
                        </CButton>
                        <CButton 
                            type="button"
                            class="btn btn-primary m-1"
                            @click.prevent="submitFormSyncGroup"
                        >
                            <span class="text-uppercase">
                                {{ $t('workflow.sync_group.save_update') }}
                            </span>
                        </CButton>
                    </div>
                </CCardFooter>
            </BTab>
            <BTab :title="$t('workflow.sync_group.list')">
                <CTable align="middle" responsive>
                    <table class="table table-hover">
                        <tbody v-if="listDataSyncGroups.length > 0">
                            <tr 
                                v-for="(syncGroup, index) in listDataSyncGroups" 
                                :key="index"
                            >
                                <td class="align-middle">{{ syncGroup.value.name }}</td> 
                                <td class="align-middle col-sm-1 table__td--action">
                                    <svg @click="editSyncGroup(index, syncGroup.value)"  class="me-2" xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 0 24 24" width="18px" fill="#83868C">
                                        <path d="M0 0h24v24H0V0z" fill="none"/>
                                        <path d="M14.06 9.02l.92.92L5.92 19H5v-.92l9.06-9.06M17.66 3c-.25 0-.51.1-.7.29l-1.83 1.83 3.75 3.75 1.83-1.83c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.2-.2-.45-.29-.71-.29zm-3.6 3.19L3 17.25V21h3.75L17.81 9.94l-3.75-3.75z"/>
                                    </svg>    
                                    <svg @click="removeSyncGroup(index)" class="me-2" xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 0 24 24" width="18px" fill="#83868C">
                                        <path d="M0 0h24v24H0V0z" fill="none"/>
                                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/>
                                    </svg>  
                                </td>
                            </tr>
                        </tbody>
                        <tbody v-else>
                            <tr>
                                <td colspan="2" class="align-middle text-center">
                                    {{ $t('search.no_matching_records_found') }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </CTable>
                <BAccordion v-if="state.activeTabEdit">
                    <BAccordionItem :title="$t('workflow.sync_group.edit')" visible>
                        <FormKit
                            ref="formSyncGroup"
                            type="form"
                            :actions="false"
                            incomplete-message=" "
                            @submit="handleSubmitEditSyncGroup"
                        >
                            <FormKit
                                label-class="required-label" 
                                type="text"
                                :label="$t('workflow.sync_group.name')"
                                :floating-label="false"
                                v-model="state.syncGroupDetail.name"
                                validation="required|length:1,200"
                                @input="(value) => handleInputNameEdit(value)"
                                :validation-messages="{
                                    required: `${$t('workflow.sync_group.name')} ${$t('workflow.sync_group.validate.required')}`,
                                    length: `${$t('workflow.sync_group.name')} ${$t('workflow.sync_group.validate.name_length')}`
                                }"
                            />
                            <FormKit
                                label-class="required-label" 
                                type="text"
                                :label="$t('workflow.sync_group.slug')"
                                :floating-label="false"
                                v-model="state.syncGroupDetail.slug"
                                validation="required|length:1,100"
                                :readonly="true"
                                :validation-messages="{
                                    required: `${$t('workflow.sync_group.slug')} ${$t('workflow.sync_group.validate.required')}`,
                                    length: `${$t('workflow.sync_group.slug')} ${$t('workflow.sync_group.validate.name_length')}`
                                }"
                            />
                            <div class="text-danger mb-3" v-if="checkDuplicateSlugEdit(state.syncGroupDetail.slug, state.indexEdit)">
                                {{ $t('workflow.sync_group.validate.duplicate_slug') }}
                            </div>
                        </FormKit>
                        <CCardFooter>
                            <div class="d-flex justify-content-end">
                                <CButton 
                                    type="button"
                                    class="btn btn-light border m-1"
                                    @click="closeEditSyncGroup"
                                >
                                    <span class="text-uppercase">
                                        {{ $t('workflow.sync_group.close') }}
                                    </span>
                                </CButton>
                                <CButton 
                                    type="button"
                                    class="btn btn-primary m-1"
                                    @click.prevent="submitFormSyncGroup"
                                >
                                    <span class="text-uppercase">
                                        {{ $t('workflow.sync_group.save_update') }}
                                    </span>
                                </CButton>
                            </div>
                        </CCardFooter>
                    </BAccordionItem>
                </BAccordion>
            </BTab>
        </BTabs>
    </CCol>
</template>

<script lang="ts">
import { defineComponent, ref, reactive } from 'vue'
import { useToast } from 'vue-toast-notification';
import { useI18n } from "vue-i18n";
import cloneDeep from 'lodash/cloneDeep'
import  { generateStandardSlug } from "@/utils/utils";

export default defineComponent({
    name: 'SyncGroupAdd',
    emits: ['close-modal-sync-group', 'reset-modal-sync-group', 'add-sync-group', 'edit-sync-group', 'remove-sync-group'],

    props: {
        dataSyncGroup: {
            type: Object,
            default: {},
            required: true,
        },
        listDataSyncGroups: {
            type: Array as () => Array<any>, 
            required: true,
            default: () => []
        },
    },

    setup(props: any, {emit}) {
        const { t }  = useI18n();
        const $toast = useToast();

        const formSyncGroup: any = ref(null);

        const state = reactive({
            tabIndex: 0,
            activeTabEdit: false,
            indexEdit: null as any,
            syncGroupDetail: {} as any,
        });

        const submitFormSyncGroup = () => {
			const node = formSyncGroup.value.node
			node.submit()
		}

        const closeFormSyncGroup = () => {
            emit('close-modal-sync-group');
		}

        const handleSubmitFormSyncGroup = async () => {
            if (!checkDuplicateSlugAdd(props.dataSyncGroup.slug)) {
                emit("add-sync-group", props.dataSyncGroup);
                emit('reset-modal-sync-group');
                state.tabIndex = 1;
                $toast.open({
                    message: t('toast.status.ACTION_SUCCESS'),
                    type: "success",
                    duration: 5000,
                    dismissible: true,
                    position: "bottom-right",
                });
            }
        }

        const editSyncGroup = (index: number, syncGroup: object): void => {
            state.indexEdit = index;
            state.syncGroupDetail = cloneDeep(syncGroup); 
            state.activeTabEdit = true;
		}

        const removeSyncGroup = (index: number): void => {
            const slugValue = props.listDataSyncGroups[index].value.slug;
			props.listDataSyncGroups.splice(index, 1);
            emit("remove-sync-group", props.listDataSyncGroups, slugValue);
            closeEditSyncGroup();
		}

        const closeEditSyncGroup = () => {
            state.activeTabEdit = false;
        }

        const handleSubmitEditSyncGroup = async () => {
            if (!checkDuplicateSlugEdit(state.syncGroupDetail.slug, state.indexEdit)) {
                const slugValue = props.listDataSyncGroups[state.indexEdit].value.slug;
                emit("edit-sync-group", state.syncGroupDetail, state.indexEdit, slugValue);
                closeEditSyncGroup();
                $toast.open({
                    message: t('toast.status.ACTION_SUCCESS'),
                    type: "success",
                    duration: 5000,
                    dismissible: true,
                    position: "bottom-right",
                });
            }
        }

        const handleInputNameAdd = async (valueName: any) => {
            if (!valueName) {
                props.dataSyncGroup.slug = '';
			} else {
    			props.dataSyncGroup.slug = generateStandardSlug(valueName);
			}
		}

        const handleInputNameEdit = async (valueName: any) => {
            if (!valueName) {
                state.syncGroupDetail.slug = '';
			} else {
    			state.syncGroupDetail.slug = generateStandardSlug(valueName);
			}
		}

        const checkDuplicateSlugAdd = (slug: string): boolean => {
            return props.listDataSyncGroups.some((item: any) => item.value.slug === slug);
        }

        const checkDuplicateSlugEdit = (slug: string, indexEdit: number): boolean => {
            return props.listDataSyncGroups.some((item: any, index: number) => {
                // Bỏ qua kiểm tra nếu là chính slug hiện tại (indexEdit)
                if (index === indexEdit) {
                    return false;
                }
                return item.value.slug === slug;
            });
        }

        return {
            state,
            formSyncGroup,
            submitFormSyncGroup,
            closeFormSyncGroup,
            handleSubmitFormSyncGroup,
            editSyncGroup,
            removeSyncGroup,
            closeEditSyncGroup,
            handleSubmitEditSyncGroup,
            handleInputNameAdd,
            handleInputNameEdit,
            checkDuplicateSlugAdd,
            checkDuplicateSlugEdit
        }
    },
});
</script>
<style type="text/css" scoped>
svg {
    cursor: pointer;
}
.table__td--action {
    min-width: 70px !important;
}
</style>