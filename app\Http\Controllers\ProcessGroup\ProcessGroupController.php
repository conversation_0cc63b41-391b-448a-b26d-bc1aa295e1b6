<?php

namespace App\Http\Controllers\ProcessGroup;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Repositories\ProcessGroup\ProcessGroupRepositoryInterface;
use App\Enums\ProcessGroupStatus;
use Illuminate\Support\Facades\Auth;

class ProcessGroupController extends Controller
{
    private $processGroupRepository;

    public function __construct(
        ProcessGroupRepositoryInterface $processGroupRepository,
    ) 
    {
        $this->processGroupRepository  = $processGroupRepository;
    }

    public function storeProcessGroup(Request $request)
    {
        $form_process_group = json_decode($request->formProcessGroup, true);
        $data_process_group = [
            'name'      => $form_process_group['name'],
            'is_active' => ProcessGroupStatus::True->value,
            'create_by' => Auth::id(),
        ];
        $data_process_group = $this->processGroupRepository->create($data_process_group);

        return response()->json([
            'status' => 'success',
        ], 201);
    }
}
