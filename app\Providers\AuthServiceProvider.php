<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;
use Laravel\Passport\Passport;
use App\Models\SaveJob;
use App\Policies\SaveJobPolicy;
use App\Models\User;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        SaveJob::class => SaveJobPolicy::class,
    ];

    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();
        
        // Trong Laravel 11, không sử dụng Passport::routes()
        // Thay vào đó, Laravel sẽ tự động đăng ký các routes cần thiết

        Gate::before(function ($user, $ability) {
            if ($user && $user->hasRole(config('constants.auth.super_admin'))) {
                return true;
            }
            
            return null;
        });

        $permissions = [
            'SUPER-ADMIN-SETTING',
            'ADMIN-SETTING',
            'VIEW-WORKFLOW',
            'CREATE-WORKFLOW',
            'VIEW-JOB',
            'CREATE-JOB',
            'VIEW-DASHBOARD',
         ];
 
         foreach ($permissions as $permission) {
            Gate::define($permission, function (User $user) use ($permission) {
                return $user->hasPermission($permission);
            });
         }

        // Thiết lập thời gian hết hạn token
        Passport::tokensExpireIn(now()->addDays(15));
        Passport::refreshTokensExpireIn(now()->addDays(30));
        Passport::personalAccessTokensExpireIn(now()->addMonths(6));
    }
} 