<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use App\Traits\ActiveGlobalScopeTrait;
use App\Traits\HasTenantTrait;

class ProcessGroup extends Model
{
    use ActiveGlobalScopeTrait, HasUuids, HasTenantTrait;
    protected $table = 'process_groups';
    protected $primaryKey = 'id';

    protected $fillable = [
        'name',
        'is_active',
        'tenant_id',
        'create_by',
    ];
    
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
}
