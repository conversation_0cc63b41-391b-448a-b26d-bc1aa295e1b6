@tailwind base;
@tailwind components;
@tailwind utilities;

/* css table-responsive */
.table-responsive::-webkit-scrollbar {
    -webkit-appearance: none;
}
.table-responsive::-webkit-scrollbar:vertical {
    width: 10px;
}
.table-responsive::-webkit-scrollbar:horizontal {
    height: 10px;
}
.table-responsive::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, .2);
    background: rgba(0,0,0,.2);
    border-radius: 10px;
    border: 2px solid #ffffff;
}
.table-responsive::-webkit-scrollbar-track {
    border-radius: 10px;  
    background-color: #ffffff; 
}
.table-responsive::-webkit-scrollbar-thumb:hover {
    background: rgba(0,0,0,.5);
}
/* end css table-responsive */

/* css scrollbar*/
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}
  
::-webkit-scrollbar-thumb {
    background-color: #888;
    border-radius: 5px;
}
  
::-webkit-scrollbar-thumb:hover {
    background-color: #555;
}
  
::-webkit-scrollbar-track {
    background: #f1f1f1;
}
  
::-webkit-scrollbar-corner {
    background: #f1f1f1;
}

/* end css scrollbar*/

/* css material-icons */
.material-icons-outlined {
    color: #83868C;
    font-size: 20px !important;
}

.material-symbols-outlined {
    color: #83868C;
    font-size: 20px !important;
}
/* end css material-icons */

/* css nav-icon  */
.sidebar-nav .nav-icon {
    font-size: 24px !important;
}
/* end css nav-icon */

/* css formkit */
.formkit-input, .formkit-decorator {
    background-color: #fff !important; 
}

.formkit-wrapper {
    max-width: none !important;
}

.formkit-legend {
    margin-bottom: 0.5em !important;
    font-weight: 400 !important;
}

.formkit-label {
    font-weight: 400 !important;
}

.formkit-label.required-label::after, .formkit-legend.required-label::after {
    content: '*';
    color: red;
    margin-left: 4px;
}
/* end css formkit */

/* css table */
.table thead th {
    font-weight: 500;
    color: #83868c;
    font-size: 14px;
}

.table tbody td {
    font-size: 14px;
}

.table > :not(caption) > * > * {
    padding: 0.75rem 0.3rem !important;
}

.table-job-add > :not(caption) > * > * {
    padding: 0.2rem 0.3rem !important;
}

.table .formkit-input {
    height: 2.9em !important;
}
/* end css table */

/* css file-pond */
.file-pond-children .filepond--drop-label {
    min-height: 2.5em !important;
}

.file-pond-children .filepond--drop-label:before {
    content: "upload";
    font-family: "Material Symbols Outlined";
    font-size: 25px !important;
    vertical-align: middle;
    margin-bottom: 2px;
    color: #c8cdcf;
}
  
.filepond--panel-root {
    background-color: #ffffff !important;
    border: 2px dashed #c8cdcf !important;
    border-radius: 10px;
    transition: border-color 0.3s ease;
}

.filepond--drop-label {
    color: #c8cdcf !important;
}

.filepond--drop-label:before {
    content: "cloud_upload";
    font-family: "Material Symbols Outlined";
    font-size: 36px !important;
    vertical-align: middle;
    margin-bottom: 8px;
    color: #c8cdcf;
}
.filepond--drop-label:hover {
    color: #b2b5b6 !important;
}

.filepond--item, .filepond--item-panel {
    max-height: 5em;
    min-height: 5em;
    overflow: hidden; 
    display: flex;
    align-items: center;
    margin-bottom: 0.5em;
    border-radius: 5px;
    margin-bottom: 0.5em; 
    transition: background-color 0.3s ease;
}

.filepond--image-preview {
    background-color: #f1eded !important;
}

.filepond--image-preview-overlay-idle {
    color: #c8cdcf !important;
}

.filepond--image-clip {
    width: 60px !important;
}

.filepond--image-preview-wrapper {
    border-radius: 0em !important;
}
/* css file-pond */

/* css input*/
textarea {
    min-height: 0em !important;
}

input[type="time"] {
    height: 2.5em !important;
}
/* end css input*/

.card-header span {
    font-weight: 500;
}

/* css bg*/
.bg-primary {
    background-image: linear-gradient(45deg,var(--cui-primary-start,#70b8ff) 0%,var(--cui-primary-stop,#2e8ae6 100%));
}

.bg-warning {
    background-image: linear-gradient(45deg,var(--cui-warning-start,#ffdb4d) 0%,var(--cui-warning-stop,#e6b800 100%));
}

.bg-success {
    background-image: linear-gradient(45deg,var(--cui-success-start,#85dbad) 0%,var(--cui-success-stop,#49b87c 100%));
}

.bg-danger {
    background-image: linear-gradient(45deg,var(--cui-danger-start,#f4739a) 0%,var(--cui-danger-stop,#d73263 100%));
}

/* end css bg*/

.form-control {
    padding: 0.6rem 0.75rem !important;
    border-color: #d1d5db !important;
}

.filepond--root {
    margin-bottom: 0.3em !important;
}
.text-danger {
    font-size: 0.8125em !important;
    color: #ea0000 !important;
}

.wrapper {
    background-color: #edf1f8;
}

/*css menu tab*/

.card {
    border-color: #00000010 !important;
}

.card-header {
    background-color: #fff !important;
}

.btn-light {
    display: flex;
    align-items: center;
    gap: 4px;
    text-decoration: none;
    padding: 8px 12px; 
}

.material-symbols-outlined {
    font-size: 24px;
    line-height: 1;
}

.fw-normal {
    font-size: 16px;
    line-height: 1;
}

.dropdown-menu-tab {
    margin-left: -1rem !important;
    margin-top: 7px;
}

.nav a {
    font-weight: 600;
    color: #83868c;
}

.nav a:hover {
    cursor: pointer;
}

.nav .active {
    display: inline-block;
    padding: 10px 10px;
    font-size: 16px;
    text-align: center;
    text-decoration: none;
    color: #0b57d0 !important;
    background-color: #fff !important;
    border-bottom: 3px solid #0b57d0;
    font-weight: 600;
    line-height: 20px;
}

/*end css menu tab*/

/*css menu page*/
.paginate .material-icons-outlined{
    font-size: 25px !important;
    color: #8b8f96;
}

.dropdown-menu-page .dropdown-menu {
    --bs-dropdown-min-width: 7rem !important;
}

.dropdown-menu-page {
    margin-top: 12px;
}
.active-page {
    color: #8b8f96 !important;
}
/*end css menu page*/

.accordion-button:not(.collapsed) {
    background-color: #fff !important;
}

/* css modal */
.modal-fullscreen .modal-dialog {
    max-width: 90%;
    margin-right: 7%;
    margin-left: 7%;
}
.modal-fullscreen .modal-content {
    border-radius: 8px !important;
}
/* end css modal */

.table__td--from-stage {
    min-width: 200px !important;
}
.table__td--action-next {
    min-width: 200px !important;
}
.table__td--to-stage {
    min-width: 200px !important;
}
.table__td--condition {
    min-width: 350px !important;
}
.table__td--condition-status {
    width: 150px !important;
}
.table__td--email-template {
    min-width: 160px !important;
}
.table__td--sync-group {
    min-width: 150px !important;
}
.table__td--process-transition {
    min-width: 200px !important;
}
.table__td--action-back-to {
    min-width: 150px !important;
}
.table__td--email-template-back {
    min-width: 160px !important;
}

.nav-link.disabled {
    display: none !important;
}

.multiselect-dropdown {
    max-height: 30rem !important;
}

.card-footer {
    z-index: 99 !important;
    position: sticky !important;
    left: 0px !important;
    bottom: 0px !important;
    width: 100% !important;
    background-color:#f8f9fa !important;
    padding: 10px !important;
}