<template>
	<CRow>
		<CCol :xs="12">
			<CCard class="mb-1">
				<CCardHeader>
					<div class="d-flex justify-content-start align-items-center">
						<b-dropdown size="lg" variant="link" toggle-class="text-decoration-none" no-caret class="dropdown-menu-tab">
							<template #button-content>
								<span class="material-icons-outlined">tune</span>
							</template>
							<b-dropdown-item
								v-for="(tab, index) in optionTabs"
								:key="index"
								@click="handleClickTab(tab.value)"
							>
								{{ tab.title }} ({{ dataCounts[tab.value] }})
							</b-dropdown-item>
							<b-dropdown-divider></b-dropdown-divider>
							<b-dropdown-item>
                                <div class="d-inline-flex align-items-center">
                                    <span class="material-symbols-outlined">auto_fix_high</span>
                                    <span class="m-2">{{ $t('menu_tab.custom') }}</span>
                                </div>
                            </b-dropdown-item>
						</b-dropdown>
						<ul class="nav">
							<li v-for="(tab, index) in optionTabs" :key="index" class="nav-item">
								<a
									:class="{ active: isActiveTab(tab.value) }"
									class="nav-link"
									@click="handleClickTab(tab.value)"
								>
									{{ tab.title }} ({{ dataCounts[tab.value] }})
								</a>
							</li>
						</ul>
						<div class="ms-auto">
							<CButton
								class="btn btn-light d-flex align-items-center"
								@click="handleClickAddTenant"
							>
								<span class="material-symbols-outlined me-1">add_circle</span>
								<span class="fw-normal">{{ $t('tenant.add') }} </span>
							</CButton>
						</div>
					</div>
				</CCardHeader>
			</CCard>
			<CCard class="mb-4">
				<CCardHeader>
					<paginate
						:meta="paginate.meta"
						:links="paginate.links"
						@page="page"
						@per-page="perPage"
					>
					</paginate>
				</CCardHeader>
				<CCardBody>
					<tenant-table
						:dataTenants="dataTenants"
						@update-data-paginate="updateDataPaginate"
						@edit-tenant="handleEditTenant"
						@delete-tenant="handleDeleteTenant"
					>
					</tenant-table>
				</CCardBody>
			</CCard>
		</CCol>
	</CRow>

	<!-- Modal for Adding/Editing Tenant -->
	<CModal
		:visible="state.showAddModal"
		@close="closeAddModal"
		size="lg"
		backdrop="static"
	>
		<CModalHeader>
			<CModalTitle>{{ state.editingTenant ? $t('tenant.edit') : $t('tenant.add') }}</CModalTitle>
		</CModalHeader>
		<CModalBody>
			<tenant-add
				:editData="state.editingTenant"
				@close-modal="closeAddModal"
				@tenant-added="handleTenantAdded"
				@tenant-updated="handleTenantUpdated"
			/>
		</CModalBody>
	</CModal>

	<!-- Modal for Delete Confirmation -->
	<CModal
		:visible="state.showDeleteModal"
		@close="closeDeleteModal"
		size="sm"
	>
		<CModalHeader>
			<CModalTitle>{{ $t('common.confirm_delete') }}</CModalTitle>
		</CModalHeader>
		<CModalBody>
			<p>{{ $t('tenant.delete_confirmation', { name: state.deletingTenant?.name }) }}</p>
		</CModalBody>
		<CModalFooter>
			<CButton color="secondary" @click="closeDeleteModal">
				{{ $t('common.cancel') }}
			</CButton>
			<CButton color="danger" @click="confirmDelete" :disabled="state.isDeleting">
				<CSpinner v-if="state.isDeleting" size="sm" class="me-2" />
				{{ $t('common.delete') }}
			</CButton>
		</CModalFooter>
	</CModal>
</template>

<script lang="ts">
import { defineComponent, reactive, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router';
import { useI18n } from "vue-i18n";
import { useToast } from 'vue-toast-notification';
import TenantTable from '@/views/admin/tenant/TenantTable.vue';
import TenantAdd from '@/views/admin/tenant/TenantAdd.vue';
import useTenants from '@/composables/tenant';
import { TENANT_STATUS } from "@/constants/constants";
import Paginate from '@/views/paginate/Paginate.vue';

export default defineComponent({
    name: "TenantListData",

	components: {
		TenantTable,
		TenantAdd,
		Paginate
	},
  
    setup() {
		const route = useRoute();
        const { t }  = useI18n();
		const $toast = useToast();

		const state = reactive({
			tabActived: '' as any,
			paginate: {
				page: 1,
		        perPage: 10,
			},
			showAddModal: false,
			showDeleteModal: false,
			editingTenant: null as any,
			deletingTenant: null as any,
			isDeleting: false
		});

		// Sử dụng computed để optionTabs cập nhật khi ngôn ngữ thay đổi
		const optionTabs = computed(() => [
			{ value: TENANT_STATUS.ALL, title: t('option_tab_tenant.all') },
            { value: TENANT_STATUS.ACTIVE, title: t('option_tab_tenant.active') },
			{ value: TENANT_STATUS.UNACTIVE, title: t('option_tab_tenant.unactive') },
		]);

		const isActiveTab = (valueTabActived: string) => {
			return route.query.tab === valueTabActived;
		};

		const handleClickTab = (valueTabActived: string) => {
			state.tabActived = valueTabActived;
			getAllTenants(
				state.paginate.page,
				state.paginate.perPage,
				state.tabActived
			);
		};

		const { setIsLoading, paginate, dataTenants, dataCounts, getAllTenants, deleteTenant } = useTenants();

		const refreshData = (): void => {
			state.tabActived = route.query.tab || TENANT_STATUS.ALL;
			getAllTenants(
				state.paginate.page,
				state.paginate.perPage,
				state.tabActived
			);
        };

        onMounted(() => {
		 	refreshData();
		})

        const page = (page: number): void => {
            getAllTenants(
				page,
				state.paginate.perPage,
				state.tabActived
			);
        };

        const perPage = (perPage: number): void => {
            state.paginate.perPage = perPage;
            getAllTenants(
				state.paginate.page,
				perPage,
				state.tabActived
			);
        };

		const updateDataPaginate = (value: any): void => {
			getAllTenants(
				value.currentPage,
				value.perPage,
				state.tabActived
			);
			setIsLoading.value = false;
		}

		const handleClickAddTenant = (): void => {
			state.editingTenant = null;
			state.showAddModal = true;
		}

		const closeAddModal = (): void => {
			state.showAddModal = false;
			state.editingTenant = null;
		}

		const handleTenantAdded = (): void => {
			refreshData(); // Refresh the tenant list after adding
		}

		const handleTenantUpdated = (): void => {
			refreshData(); // Refresh the tenant list after updating
		}

		const handleEditTenant = (tenant: any): void => {
			state.editingTenant = tenant;
			state.showAddModal = true;
		}

		const handleDeleteTenant = (tenant: any): void => {
			state.deletingTenant = tenant;
			state.showDeleteModal = true;
		}

		const closeDeleteModal = (): void => {
			state.showDeleteModal = false;
			state.deletingTenant = null;
		}

		const confirmDelete = async (): Promise<void> => {
			if (!state.deletingTenant) return;

			state.isDeleting = true;
			try {
				const response = await deleteTenant(state.deletingTenant.id);
				if (response?.status === 'success') {
					$toast.success(t('tenant.deleted_successfully'));
					refreshData();
					closeDeleteModal();
				}
			} catch (error) {
				console.error('Error deleting tenant:', error);
			} finally {
				state.isDeleting = false;
			}
		}

		return {
			state,
			optionTabs,
			setIsLoading,
			paginate,
			isActiveTab,
			handleClickTab,
			dataTenants,
			dataCounts,
			refreshData,
			page,
			perPage,
			updateDataPaginate,
			handleClickAddTenant,
			closeAddModal,
			handleTenantAdded,
			handleTenantUpdated,
			handleEditTenant,
			handleDeleteTenant,
			closeDeleteModal,
			confirmDelete,
		}
    }
});
</script>

<style scoped>

</style>