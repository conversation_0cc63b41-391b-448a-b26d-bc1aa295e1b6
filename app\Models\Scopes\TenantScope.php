<?php

namespace App\Models\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class TenantScope implements Scope
{
    public function apply(Builder $builder, Model $model)
    {
        if (auth()->check() && auth()->user()->hasRole(config('constants.auth.super_admin'))) {
            return;
        }
        
        if (app()->has('tenant')) {
            $builder->where($model->getTable().'.tenant_id', app('tenant')->id);
        }
    }
}