<template>
    <CRow>
        <CCol :xs="8">
            <CCard class="mb-4">
                <CCardBody>
                    <CForm @submit.prevent="saveRole" class="row gx-3 gy-3 align-items-center">
                        <!-- Role Name -->
                        <CCol :md="12">
                            <CFormLabel>
                                {{ $t('role.name') }} 
                                <span class="text-danger">*</span>
                            </CFormLabel>
                            <CInputGroup>
                                <CFormInput
                                    type="text"
                                    v-model="state.form.name"
                                    :placeholder="$t('role.name')"
                                    required
                                />
                            </CInputGroup>
                        </CCol>
                        
                        <!-- Permission Groups -->
                        <CCol :md="12">
                            <CFormLabel>
                                {{ $t('role.permissions') }}
                                <span class="text-danger">*</span>
                            </CFormLabel>
                            <div class="permission-groups">
                                <div v-for="(group, indexGroup) in state.permissionGroups" :key="group.id" class="permission-group mb-3">
                                    <div class="d-flex align-items-center justify-content-between mb-2">
                                        <CFormCheck
                                            :id="`group-${group.id}`"
                                            :label="group.name"
                                            :checked="isGroupSelected(group)"
                                            @change="toggleGroupPermissions(group)"
                                            class="text-uppercase"
                                        />
                                        <b-dropdown
                                            variant="link"
                                            toggle-class="text-decoration-none"
                                            no-caret
                                            class="dropdown-menu-tab"
                                        >
                                            <template #button-content>
                                                <span class="text-secondary cursor-pointer text-uppercase">{{ getScopeLabel(group.selectedScope !== undefined ? group.selectedScope : state.form.scope) }}</span>
                                            </template>
                                            <b-dropdown-item 
                                                v-for="(optionScope, indexScope) in state.selectOptionScopes"
                                                :key="indexScope" 
                                                @click="selectScope(indexGroup, optionScope.value)"
                                            >
                                                {{ optionScope.label }}
                                            </b-dropdown-item>
                                        </b-dropdown>
                                    </div>    
                                    <div class="permissions-list ms-4">
                                        <div v-for="permission in group.permissions" :key="permission.id" class="permission-item">
                                            <CFormCheck
                                                :id="`permission-${permission.id}`"
                                                :label="permission.name"
                                                :value="permission.id"
                                                v-model="state.form.permissions"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </CCol>
                        
                        <!-- Submit Button -->
                        <CCardFooter>
							<div class="d-flex justify-content-start">
								<CButton 
									type="submit"
									class="btn btn-primary m-1"
								>
									<span class="text-uppercase">
										{{ $t('workflow.stage.save_update') }}
									</span>
								</CButton>
								<CButton 
									type="button"
									class="btn btn-light border m-1"
									@click="closeAddRole"
								>
									<span class="text-uppercase">
										{{ $t('workflow.stage.close') }}
									</span>
								</CButton>
							</div>
						</CCardFooter>
                    </CForm>
                </CCardBody>
            </CCard>
        </CCol>
    </CRow>

    <loading :isLoading="setIsLoading" />
</template>

<script lang="ts">
import { defineComponent, reactive, onMounted } from 'vue'
import Loading from '@/views/loading/Loading.vue'
import useRoles from '@/composables/role'
import usePermissions from '@/composables/permission'
import { useToast } from 'vue-toast-notification'
import { useRouter } from 'vue-router'
import { useI18n } from "vue-i18n"
import { ROLE_SCOPE } from '@/constants/constants'

export default defineComponent({
    name: 'RoleAdd',

    components: {
        Loading
    },

    setup(props: any, {emit}) {
        const $toast = useToast()
        const { t } = useI18n()
        const router = useRouter()
        
        const state = reactive({
            form: {
                name: '',
                permissions: [] as (number | string)[],
                scope: ROLE_SCOPE.ALL,
            },
            permissionGroups: [] as any[],
            selectOptionScopes: [
                { label: t('role.scope.all'), value: ROLE_SCOPE.ALL },
                { label: t('role.scope.company'), value: ROLE_SCOPE.COMPANY },
                { label: t('role.scope.branch'), value: ROLE_SCOPE.BRANCH },
                { label: t('role.scope.department'), value: ROLE_SCOPE.DEPARTMENT },
			] as Array<any>,
        })

        const { getPermissionGroups } = usePermissions()
        const { setIsLoading, storeRole } = useRoles()

        const loadPermissionGroups = async (): Promise<void> => {
            try {
                const response = await getPermissionGroups()
                if (response && response.status === 'success' && response.permission_groups) {
                    state.permissionGroups = response.permission_groups.map((group: any) => ({
                        ...group,
                        selectedScope: state.form.scope
                    }))
                } else {
                    state.permissionGroups = []
                    console.error('Error loading permission groups: Invalid response structure or unsuccessful status', response)
                }
            } catch (error) {
                console.error('Error loading permission groups:', error)
                state.permissionGroups = []
            }
        }

        // Check if all permissions in a group are selected
        const isGroupSelected = (group: any): boolean => {
            if (!group.permissions || group.permissions.length === 0) return false
            return group.permissions.every((permission: any) => 
                state.form.permissions.includes(permission.id)
            )
        }

        // Toggle all permissions in a group
        const toggleGroupPermissions = (group: any): void => {
            const allSelected = isGroupSelected(group)
            
            if (allSelected) {
                // Deselect all permissions in this group
                state.form.permissions = state.form.permissions.filter(
                    (id: number | string) => !group.permissions.some((p: any) => p.id === id)
                )
            } else {
                // Select all permissions in this group
                const permissionIds = group.permissions.map((p: any) => p.id)
                const newPermissions = [...state.form.permissions]
                
                permissionIds.forEach((id: number | string) => {
                    if (!newPermissions.includes(id)) {
                        newPermissions.push(id)
                    }
                })
                
                state.form.permissions = newPermissions
            }
        }

        const saveRole = async (): Promise<void> => {
            try {
                if (!state.form.name || state.form.permissions.length === 0) {
                    $toast.open({
                        message: t('toast.error_code.FORM_INCOMPLETE'),
                        type: "error",
                        duration: 5000,
                        dismissible: true,
                        position: "bottom-right",
                    });
                    return;
                }

                const permissionsWithScope: { id: number | string, scope: string }[] = [];

                state.permissionGroups.forEach(group => {
                    if (group.permissions && group.selectedScope !== undefined) {
                        group.permissions.forEach((permission: any) => {
                            // Check if this specific permission is selected
                            if (state.form.permissions.includes(permission.id)) {
                                permissionsWithScope.push({
                                    id: permission.id,
                                    scope: group.selectedScope
                                });
                            }
                        });
                    }
                });

                const payload = {
                    name: state.form.name,
                    permissions: permissionsWithScope // This now holds {id, scope} objects for selected permissions
                };

                const result = await storeRole(payload);
                if (result && result.status === 'success') {
                    $toast.open({
                        message: t('toast.status.ACTION_SUCCESS'),
                        type: "success",
                        duration: 5000,
                        dismissible: true,
                        position: "bottom-right",
                    });
                    
                    router.push({ name: 'RoleListData' });
                }
            } catch (error) {
                console.error('Error saving role:', error);
            }
        }

        onMounted( async () => {
            await loadPermissionGroups();            
        })

        const getScopeLabel = (scope: string): string => {
            return scope == ROLE_SCOPE.ALL ? t('role.scope.all') : scope == ROLE_SCOPE.COMPANY ? t('role.scope.company') : scope == ROLE_SCOPE.BRANCH ? t('role.scope.branch') : t('role.scope.department');
        }

        const selectScope = (indexGroup: number, scope: string): void => {
            state.permissionGroups[indexGroup].selectedScope = scope
        }

        const closeAddRole = () => {
            router.push({ name: 'RoleListData' });
        }

        return {
            state,
            setIsLoading,
            saveRole,
            isGroupSelected,
            toggleGroupPermissions,
            getScopeLabel,
            selectScope,
            closeAddRole
        }
    },
})
</script>

<style type="text/css" scoped>
.cursor-pointer {
    cursor: pointer;
}
.permission-group {
    border: 1px solid #eee;
    border-radius: 5px;
    padding: 10px;
    background-color: #f9f9f9;
}
.permissions-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 10px;
}
input[type="checkbox"] {
    transform: scale(1.4) !important;
}
.dropdown-menu-tab {
    margin-top: -10px !important;
}
</style>
