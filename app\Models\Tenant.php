<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\ActiveGlobalScopeTrait;

class Tenant extends Model
{
    use HasFactory, HasUuids, ActiveGlobalScopeTrait;

    protected $table = 'tenants';
    protected $primaryKey = 'id';

    protected $fillable = [
        'name',
        'domain_name',
        'start_date',
        'end_date',
        'is_active',
        'create_by',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Relationship với User (người tạo)
     */
    public function createBy()
    {
        return $this->belongsTo(User::class, 'create_by');
    }
}
