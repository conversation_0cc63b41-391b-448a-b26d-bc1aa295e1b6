<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use App\Traits\ActiveGlobalScopeTrait;

class Field extends Model
{
    use ActiveGlobalScopeTrait, HasUuids;
    protected $table = 'fields';
    protected $primaryKey = 'id';
    protected $fillable = [
        'keyword',
        'display_name',
        'display_name_en',
        'type',
        'default_value',
        'required',
        'order',
        'min_equal',
        'max_equal',
        'stage_id',
        'placeholder',
        'placeholder_en',
        'column_width',
        'form_id',
        'not_edit',
        'multiple',
        'options',
        'object_table',
        'column_table',
        'sub_column_table',
        'parent_id',
        'is_active',
        'create_by',
    ];

    // <PERSON><PERSON>c thuộc tính cần casting
    protected $casts = [
        'options' => 'array',  // Chuyển đổi trường options từ JSON thành mảng
        'sub_column_table' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function parent()
    {
        return $this->belongsTo(Field::class, 'parent_id', 'id')->with('parent');
    }

    public function children()
    {
        return $this->hasMany(Field::class, 'parent_id', 'id')->with('children');
    }

    public function stage()
    {
        return $this->belongsTo(Stage::class, 'stage_id', 'id');
    }
}
