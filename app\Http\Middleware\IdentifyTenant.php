<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Services\TenantService;

class IdentifyTenant
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Bỏ qua cho Super Admin
        if (auth()->check() && auth()->user()->hasRole(config('constants.auth.super_admin'))) {
            return $next($request);
        }

        // Lấy tên miền từ request hiện tại
        $host = $request->getHost();

        // Sử dụng TenantService để tìm tenant theo domain name
        $tenant = TenantService::findByDomainNameOrFail($host);

        // Nếu tìm thấy, lưu thông tin đơn vị vào service container
        // để toàn bộ ứng dụng có thể truy cập dễ dàng.
        app()->singleton('tenant', function () use ($tenant) {
            return $tenant;
        });

        // Tiếp tục request
        return $next($request);
    }
}
