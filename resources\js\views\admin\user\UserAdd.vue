<template>
    Thêm người dùng
</template>

<script lang="ts">
import { defineComponent, reactive, onMounted } from 'vue'
import VueElementLoading from 'vue-element-loading'
import useRoles from '@/composables/role'
import usePermissions from '@/composables/permission'
import { useToast } from 'vue-toast-notification'
import { useRouter } from 'vue-router'
import { useI18n } from "vue-i18n"
import { ROLE_SCOPE } from '@/constants/constants'

export default defineComponent({
    name: 'RoleAdd',

    components: {
        VueElementLoading
    },

    setup(props: any, {emit}) {
        const $toast = useToast()
        const { t } = useI18n()
        const router = useRouter()
        
        const state = reactive({
            form: {
                name: '',
                permissions: [] as (number | string)[],
                scope: ROLE_SCOPE.ALL,
            },
            permissionGroups: [] as any[],
            selectOptionScopes: [
                { label: t('role.scope.all'), value: ROLE_SCOPE.ALL },
                { label: t('role.scope.company'), value: ROLE_SCOPE.COMPANY },
                { label: t('role.scope.branch'), value: ROLE_SCOPE.BRANCH },
                { label: t('role.scope.department'), value: ROLE_SCOPE.DEPARTMENT },
			] as Array<any>,
        })

        const { getPermissionGroups } = usePermissions()
        const { setIsLoading, storeRole } = useRoles()

        const loadPermissionGroups = async (): Promise<void> => {
            try {
                const response = await getPermissionGroups()
                if (response && response.status === 'success' && response.permission_groups) {
                    state.permissionGroups = response.permission_groups.map((group: any) => ({
                        ...group,
                        selectedScope: state.form.scope
                    }))
                } else {
                    state.permissionGroups = []
                    console.error('Error loading permission groups: Invalid response structure or unsuccessful status', response)
                }
            } catch (error) {
                console.error('Error loading permission groups:', error)
                state.permissionGroups = []
            }
        }

        // Check if all permissions in a group are selected
        const isGroupSelected = (group: any): boolean => {
            if (!group.permissions || group.permissions.length === 0) return false
            return group.permissions.every((permission: any) => 
                state.form.permissions.includes(permission.id)
            )
        }

        // Toggle all permissions in a group
        const toggleGroupPermissions = (group: any): void => {
            const allSelected = isGroupSelected(group)
            
            if (allSelected) {
                // Deselect all permissions in this group
                state.form.permissions = state.form.permissions.filter(
                    (id: number | string) => !group.permissions.some((p: any) => p.id === id)
                )
            } else {
                // Select all permissions in this group
                const permissionIds = group.permissions.map((p: any) => p.id)
                const newPermissions = [...state.form.permissions]
                
                permissionIds.forEach((id: number | string) => {
                    if (!newPermissions.includes(id)) {
                        newPermissions.push(id)
                    }
                })
                
                state.form.permissions = newPermissions
            }
        }

        const saveRole = async (): Promise<void> => {
            try {
                if (!state.form.name || state.form.permissions.length === 0) {
                    $toast.open({
                        message: t('toast.error_code.FORM_INCOMPLETE'),
                        type: "error",
                        duration: 5000,
                        dismissible: true,
                        position: "bottom-right",
                    });
                    return;
                }

                const permissionsWithScope: { id: number | string, scope: string }[] = [];

                state.permissionGroups.forEach(group => {
                    if (group.permissions && group.selectedScope !== undefined) {
                        group.permissions.forEach((permission: any) => {
                            // Check if this specific permission is selected
                            if (state.form.permissions.includes(permission.id)) {
                                permissionsWithScope.push({
                                    id: permission.id,
                                    scope: group.selectedScope
                                });
                            }
                        });
                    }
                });

                const payload = {
                    name: state.form.name,
                    permissions: permissionsWithScope // This now holds {id, scope} objects for selected permissions
                };

                const result = await storeRole(payload);
                if (result && result.status === 'success') {
                    $toast.open({
                        message: t('toast.status.ACTION_SUCCESS'),
                        type: "success",
                        duration: 5000,
                        dismissible: true,
                        position: "bottom-right",
                    });
                    
                    router.push({ name: 'RoleListData' });
                }
            } catch (error) {
                console.error('Error saving role:', error);
            }
        }

        onMounted( async () => {
            await loadPermissionGroups();            
        })

        const getScopeLabel = (scope: string): string => {
            return scope == ROLE_SCOPE.ALL ? t('role.scope.all') : scope == ROLE_SCOPE.COMPANY ? t('role.scope.company') : scope == ROLE_SCOPE.BRANCH ? t('role.scope.branch') : t('role.scope.department');
        }

        const selectScope = (indexGroup: number, scope: string): void => {
            state.permissionGroups[indexGroup].selectedScope = scope
        }

        const closeAddRole = () => {
            router.push({ name: 'RoleListData' });
        }

        return {
            state,
            setIsLoading,
            saveRole,
            isGroupSelected,
            toggleGroupPermissions,
            getScopeLabel,
            selectScope,
            closeAddRole
        }
    },
})
</script>

<style type="text/css" scoped>
.cursor-pointer {
    cursor: pointer;
}
.permission-group {
    border: 1px solid #eee;
    border-radius: 5px;
    padding: 10px;
    background-color: #f9f9f9;
}
.permissions-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 10px;
}
input[type="checkbox"] {
    transform: scale(1.4) !important;
}
.dropdown-menu-tab {
    margin-top: -10px !important;
}
</style>
