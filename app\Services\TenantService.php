<?php

namespace App\Services;

use App\Models\Tenant;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;

class TenantService
{
    /**
     * Tìm tenant theo domain name với kiểm tra điều kiện hiệu lực
     *
     * @param string $domainName
     * @param bool $useCache
     * @return Tenant|null
     */
    public static function findByDomainName(string $domainName, bool $useCache = true): ?Tenant
    {
        $cacheKey = "tenant_domain_{$domainName}";
        
        // Kiểm tra cache nếu được yêu cầu
        if ($useCache && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        // Lấy ngày hiện tại
        $currentDate = Carbon::now()->format('Y-m-d');

        // Tìm đơn vị theo domain VÀ kiểm tra điều kiện hiệu lực trong cùng một query
        $tenant = Tenant::where('domain_name', $domainName)
            ->where(function ($query) use ($currentDate) {
                // Điều kiện 1: start_date phải nhỏ hơn hoặc bằng ngày hiện tại
                $query->where('start_date', '<=', $currentDate);
                // Điều kiện 2: Phải thỏa mãn MỘT TRONG HAI điều kiện con sau
                $query->where(function ($subQuery) use ($currentDate) {
                    // Hoặc end_date là NULL (vô thời hạn)
                    $subQuery->whereNull('end_date')
                        // Hoặc end_date lớn hơn hoặc bằng ngày hiện tại
                        ->orWhere('end_date', '>=', $currentDate);
                });
            })
            ->first();

        // Lưu vào cache nếu tìm thấy và được yêu cầu sử dụng cache
        if ($tenant && $useCache) {
            Cache::put($cacheKey, $tenant, 3600); // Lưu trong 1 giờ
        }

        return $tenant;
    }

    /**
     * Tìm tenant theo domain name và throw exception nếu không tìm thấy
     *
     * @param string $domainName
     * @param bool $useCache
     * @return Tenant
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException
     */
    public static function findByDomainNameOrFail(string $domainName, bool $useCache = true): Tenant
    {
        $tenant = self::findByDomainName($domainName, $useCache);
        
        if (!$tenant) {
            abort(404, 'Đơn vị không tồn tại hoặc tên miền không hợp lệ hoặc đã hết hạn');
        }

        return $tenant;
    }

    /**
     * Kiểm tra xem domain name có hợp lệ không
     *
     * @param string $domainName
     * @param bool $useCache
     * @return bool
     */
    public static function isValidDomain(string $domainName, bool $useCache = true): bool
    {
        return self::findByDomainName($domainName, $useCache) !== null;
    }

    /**
     * Xóa cache của tenant theo domain name
     *
     * @param string $domainName
     * @return bool
     */
    public static function clearCache(string $domainName): bool
    {
        $cacheKey = "tenant_domain_{$domainName}";
        return Cache::forget($cacheKey);
    }

    /**
     * Refresh cache của tenant theo domain name
     *
     * @param string $domainName
     * @return Tenant|null
     */
    public static function refreshCache(string $domainName): ?Tenant
    {
        // Xóa cache cũ
        self::clearCache($domainName);
        
        // Lấy dữ liệu mới và lưu vào cache
        return self::findByDomainName($domainName, true);
    }
}
