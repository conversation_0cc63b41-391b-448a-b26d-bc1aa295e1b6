/**
 * L<PERSON>y giá trị của một biến môi trường một cách an toàn.
 * Sẽ báo lỗi nếu biến không được định nghĩa trong tệp .env.
 *
 * @param {string} key Tên của biến môi trường.
 * @returns {string} Giá trị của biến.
 */
export function getEnv(key) {
    const value = import.meta.env[key];

    if (value === undefined) {
        throw new Error(`Biến môi trường '${key}' chưa được thiết lập.`);
    }

    return value;
}

/**
 * Một đối tượng chứa các biến môi trường đã được xác thực của ứng dụng.
 * Giúp truy cập các biến một cách tiện lợi và an toàn.
 */
export const env = {
    tokenCookieName: getEnv('VITE_TOKEN_COOKIE_NAME'),
};