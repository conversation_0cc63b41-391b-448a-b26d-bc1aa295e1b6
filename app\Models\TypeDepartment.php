<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Traits\ActiveGlobalScopeTrait;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use App\Traits\HasTenantTrait;

class TypeDepartment extends Model
{
    use HasUuids, ActiveGlobalScopeTrait, HasTenantTrait;
    
    protected $table = 'type_departments';
    protected $primaryKey = 'id';

    protected $fillable = [
        'name',
        'is_active',
        'tenant_id',
        'create_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
}
