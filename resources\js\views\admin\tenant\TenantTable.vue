<template>
    <CTable hover responsive>
        <CTableHead>
            <CTableRow>
                <CTableHeaderCell scope="col">{{ $t('tenant.name') }}</CTableHeaderCell>
                <CTableHeaderCell scope="col">{{ $t('tenant.domain_name') }}</CTableHeaderCell>
                <CTableHeaderCell scope="col">{{ $t('tenant.start_date') }}</CTableHeaderCell>
                <CTableHeaderCell scope="col">{{ $t('tenant.end_date') }}</CTableHeaderCell>
                <CTableHeaderCell scope="col">{{ $t('tenant.status') }}</CTableHeaderCell>
                <CTableHeaderCell scope="col">{{ $t('tenant.create_by') }}</CTableHeaderCell>
                <CTableHeaderCell scope="col">{{ $t('tenant.created_at') }}</CTableHeaderCell>
            </CTableRow>
        </CTableHead>
        <CTableBody v-if="checkDataNotEmpty">
            <CTableRow v-for="tenant in dataTenants" :key="tenant.id">
                <CTableDataCell>{{ tenant.name }}</CTableDataCell>
                <CTableDataCell>{{ tenant.domain_name }}</CTableDataCell>
                <CTableDataCell>{{ formatDate(tenant.start_date) }}</CTableDataCell>
                <CTableDataCell>{{ tenant.end_date ? formatDate(tenant.end_date) : $t('tenant.unlimited') }}</CTableDataCell>
                <CTableDataCell>
                    <CBadge :color="tenant.is_active ? 'success' : 'danger'">
                        {{ tenant.is_active ? $t('common.active') : $t('common.inactive') }}
                    </CBadge>
                </CTableDataCell>
                <CTableDataCell>{{ tenant.create_by?.full_name || $t('common.no_data') }}</CTableDataCell>
                <CTableDataCell>{{ formatDateTime(tenant.created_at) }}</CTableDataCell>
            </CTableRow>
        </CTableBody>
        <CTableBody v-else>
            <CTableRow>
                <CTableDataCell colspan="7" class="text-center">
                    {{ $t('search.no_matching_records_found') }}
                </CTableDataCell>
            </CTableRow>
        </CTableBody>
    </CTable>
    
</template>

<script lang="ts">
import { defineComponent, PropType, computed } from 'vue'
import { useI18n } from "vue-i18n";
import moment from 'moment';

export default defineComponent({
    name: "TenantTable",
    
    props: {
        dataTenants: {
            type: Array as PropType<any[]>,
            required: true
        }
    },

    emits: ['update-data-paginate'],

    setup(props, { emit }) {
        const { t } = useI18n();

        const checkDataNotEmpty = computed<boolean>(() => {
            return props.dataTenants.length > 0;
        });

        const formatDate = (date: string) => {
            return moment(date).format('DD/MM/YYYY');
        };

        const formatDateTime = (date: string) => {
            return moment(date).format('HH:mm DD/MM/YYYY ');
        };

        return {
            checkDataNotEmpty,
            formatDate,
            formatDateTime,
        }
    }
});
</script>

<style scoped>
</style>
