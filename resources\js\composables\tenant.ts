import axios from 'axios';
import { ref, reactive } from 'vue';
import { useToast } from 'vue-toast-notification';
import { useRouter } from 'vue-router';
import { useI18n } from "vue-i18n";
import { Meta, Link } from "@/types/index";

export default function useTenants() {
    const $toast = useToast();
    const router = useRouter();
    const { t }  = useI18n();

    const setIsLoading = ref(false);
    const paginate = reactive({
        meta: {
            from: '',
            to: '',
            total: '',
            lastPage: '',
            currentPage: '',
            perPage: '',
        } as Meta,
        links: {
            prev: '',
            next: '',
        } as Link,
    });

    const dataTenants = ref([]);
    const dataCounts = ref({});

    const catchError = (error: any) => {
        if (error.response && error.response.data && error.response.data.message) {
            $toast.error(error.response.data.message);
        } else {
            $toast.error(t('message.error_occurred'));
        }
    };

    const storeTenant = async (formTenantData: any) => {
        setIsLoading.value = true;
        try {
            let response = await axios.post(`/api/tenants`, formTenantData);
            return response?.data;
        } catch (error: any) {
            if (!error.response) {
                setTimeout(() => {
                    storeTenant(formTenantData);
                }, 1000);

                return;
            }

            catchError(error);
            throw error;
        } finally {
            setIsLoading.value = false;
        }
    };

    const getAllTenants = async (
        page: number, 
        perPage: number,
        valueTabActived: string,
    ) => {
        setIsLoading.value = true;
        try {
            const tab = valueTabActived !== '' ? { tab: valueTabActived } : null;
            let response = await axios.get('/api/tenants', {
                params: {
                    tab: tab !== null ? valueTabActived : tab,
                    page: page,
                    perPage: perPage,
                }
            });

            if (response?.data?.status === 'success') {
                dataTenants.value = response.data.tenants.data || response.data.tenants;
                dataCounts.value = response.data.counts;
                
                if (response.data.tenants.meta) {
                    paginate.meta = response.data.tenants.meta;
                    paginate.links = response.data.tenants.links;
                }
            }

            return response?.data;
        } catch (error: any) {
            console.log(error);
            if (!error.response) {
                setTimeout(() => {
                    getAllTenants(page, perPage, valueTabActived);
                }, 1000);

                return;
            }

            catchError(error);
        } finally {
            setIsLoading.value = false;
        }
    };

    const updateTenant = async (tenantId: string, formTenantData: any) => {
        setIsLoading.value = true;
        try {
            let response = await axios.put(`/api/tenants/${tenantId}`, formTenantData);
            return response?.data;
        } catch (error: any) {
            if (!error.response) {
                setTimeout(() => {
                    updateTenant(tenantId, formTenantData);
                }, 1000);

                return;
            }

            catchError(error);
            throw error;
        } finally {
            setIsLoading.value = false;
        }
    };

    const deleteTenant = async (tenantId: string) => {
        setIsLoading.value = true;
        try {
            let response = await axios.delete(`/api/tenants/${tenantId}`);
            return response?.data;
        } catch (error: any) {
            if (!error.response) {
                setTimeout(() => {
                    deleteTenant(tenantId);
                }, 1000);

                return;
            }

            catchError(error);
            throw error;
        } finally {
            setIsLoading.value = false;
        }
    };

    const getTenant = async (tenantId: string) => {
        setIsLoading.value = true;
        try {
            let response = await axios.get(`/api/tenants/${tenantId}`);
            return response?.data;
        } catch (error: any) {
            if (!error.response) {
                setTimeout(() => {
                    getTenant(tenantId);
                }, 1000);

                return;
            }

            catchError(error);
        } finally {
            setIsLoading.value = false;
        }
    };

    return {
        setIsLoading,
        paginate,
        dataTenants,
        dataCounts,
        getAllTenants,
        storeTenant,
        updateTenant,
        deleteTenant,
        getTenant
    }
}
