<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Repositories\Form\FormRepositoryInterface;
use App\Repositories\Form\FormRepository;
use App\Repositories\Field\FieldRepositoryInterface;
use App\Repositories\Field\FieldRepository;
use App\Repositories\SaveJob\SaveJobRepositoryInterface;
use App\Repositories\SaveJob\SaveJobRepository;
use App\Repositories\JobFieldValue\JobFieldValueRepositoryInterface;
use App\Repositories\JobFieldValue\JobFieldValueRepository;
use App\Repositories\JobFieldValueObject\JobFieldValueObjectRepositoryInterface;
use App\Repositories\JobFieldValueObject\JobFieldValueObjectRepository;
use App\Repositories\ModelSystem\ModelSystemRepositoryInterface;
use App\Repositories\ModelSystem\ModelSystemRepository;
use App\Repositories\Process\ProcessRepositoryInterface;
use App\Repositories\Process\ProcessRepository;
use App\Repositories\ProcessGroup\ProcessGroupRepositoryInterface;
use App\Repositories\ProcessGroup\ProcessGroupRepository;
use App\Repositories\ProcessVersion\ProcessVersionRepositoryInterface;
use App\Repositories\ProcessVersion\ProcessVersionRepository;
use App\Repositories\Stage\StageRepositoryInterface;
use App\Repositories\Stage\StageRepository;
use App\Repositories\Condition\ConditionRepositoryInterface;
use App\Repositories\Condition\ConditionRepository;
use App\Repositories\Action\ActionRepositoryInterface;
use App\Repositories\Action\ActionRepository;
use App\Repositories\StageTransition\StageTransitionRepositoryInterface;
use App\Repositories\StageTransition\StageTransitionRepository;
use App\Repositories\EmailTemplate\EmailTemplateRepositoryInterface;
use App\Repositories\EmailTemplate\EmailTemplateRepository;
use App\Repositories\User\UserRepositoryInterface;
use App\Repositories\User\UserRepository;
use App\Repositories\JobApprovalHistory\JobApprovalHistoryRepositoryInterface;
use App\Repositories\JobApprovalHistory\JobApprovalHistoryRepository;
use App\Services\JobPermissionService;
use App\Services\ProcessConditionService;
use App\Services\StageTransitionService;
use App\Services\EmailService;
use App\Services\JobApprovalService;
use App\Services\DataProcessingService;
use App\Services\WorkflowProcessService;
use App\Services\FormDataProcessingService;
use App\Services\FileUploadService;
use App\Services\JobCreationService;
use App\Providers\ServiceServiceProvider;
use App\Repositories\Permission\PermissionRepositoryInterface;
use App\Repositories\Permission\PermissionRepository;
use App\Repositories\Role\RoleRepositoryInterface;
use App\Repositories\Role\RoleRepository;
use App\Repositories\Tenant\TenantRepositoryInterface;
use App\Repositories\Tenant\TenantRepository;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Đăng ký ServiceServiceProvider
        $this->app->register(ServiceServiceProvider::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Đăng ký các Repository
        $this->app->singleton(
            FormRepositoryInterface::class,
            FormRepository::class
        );
        $this->app->singleton(
            FieldRepositoryInterface::class,
            FieldRepository::class
        );
        $this->app->singleton(
            SaveJobRepositoryInterface::class,
            SaveJobRepository::class
        );
        $this->app->singleton(
            JobFieldValueRepositoryInterface::class,
            JobFieldValueRepository::class
        );
        $this->app->singleton(
            JobFieldValueObjectRepositoryInterface::class,
            JobFieldValueObjectRepository::class
        );
        $this->app->singleton(
            ModelSystemRepositoryInterface::class,
            ModelSystemRepository::class
        );
        $this->app->singleton(
            ProcessRepositoryInterface::class,
            ProcessRepository::class
        );
        $this->app->singleton(
            ProcessGroupRepositoryInterface::class,
            ProcessGroupRepository::class
        );
        $this->app->singleton(
            ProcessVersionRepositoryInterface::class,
            ProcessVersionRepository::class
        );
        $this->app->singleton(
            StageRepositoryInterface::class,
            StageRepository::class
        );
        $this->app->singleton(
            ConditionRepositoryInterface::class,
            ConditionRepository::class
        );
        $this->app->singleton(
            ActionRepositoryInterface::class,
            ActionRepository::class
        );
        $this->app->singleton(
            StageTransitionRepositoryInterface::class,
            StageTransitionRepository::class
        );
        $this->app->singleton(
            EmailTemplateRepositoryInterface::class,
            EmailTemplateRepository::class
        );
        $this->app->singleton(
            UserRepositoryInterface::class,
            UserRepository::class
        );
        $this->app->singleton(
            JobApprovalHistoryRepositoryInterface::class,
            JobApprovalHistoryRepository::class
        );
        
        // Đăng ký các service
        $this->app->singleton(JobPermissionService::class);
        $this->app->singleton(ProcessConditionService::class);
        $this->app->singleton(DataProcessingService::class);
        $this->app->singleton(FileUploadService::class);
        $this->app->singleton(JobCreationService::class);
        
        // Đăng ký các service phụ thuộc
        $this->app->singleton(StageTransitionService::class);
        $this->app->singleton(EmailService::class);
        $this->app->singleton(JobApprovalService::class);
        $this->app->singleton(FormDataProcessingService::class);
        $this->app->singleton(WorkflowProcessService::class);

        $this->app->singleton(
            PermissionRepositoryInterface::class,
            PermissionRepository::class
        );
        $this->app->singleton(
            RoleRepositoryInterface::class,
            RoleRepository::class
        );
        $this->app->singleton(
            TenantRepositoryInterface::class,
            TenantRepository::class
        );
    }
}
