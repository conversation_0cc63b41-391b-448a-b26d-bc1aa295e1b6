const WORKFLOWS = {
    STAGE: {
        START: 'start',
        DONE: 'done',
        FALSE: 'false',
    },
    ACTION: {
        CREATE: 'create',
        BACK_TO: 'back_to',
    },
    CONDITION: {
        TRUE: 'true',
        FALSE: 'false',
        OPERATOR: {
            AND: 'and',
            OR: 'or',
        },
        OPTION_OBJECT: {
            FORM: 'form',
            PROCESS: 'process',
        },
    },
    OPTION_SYSTEM_DEFAULT: {
        CREATE_BY_ID: 'create_by_id',
        USER_ID: 'user_id',
        APPROVE_BY_ID: 'approve_by_id',
    },
    STATUS: {
        ALL: 'all',
        ACTIVE: 'active',
        SAVE_DRAFT: 'save_draft',
        UNACTIVE: 'unactive',
    },
    STRING: {
        ACTION_NEXT: 'action_next',
        STAGE: 'stage',
        CONDITION: 'condition',
        SYNC_GROUP: 'sync_group',
        EMAIL_TEMPLATE: 'email_template',
        PROCESS_TRANSITION: 'process_transition',
    },
};

const SAVE_JOB_STATUS = {
    ALL: 'all',
    PENDING: 'pending',
    PROCESSING: 'processing',
    COMPLETED: 'completed',
    CANCEL: 'cancel',
};

const TAB_JOB_DETAIL = {
    DETAIL: 'detail',
    FILES: 'files',
};

const PERMISSION_STATUS = {
    ALL: 'all',
    ACTIVE: 'active',
    UNACTIVE: 'unactive',
    TRUE: 1,
    FALSE: 0,
};

const ROLE_STATUS = {
    ALL: 'all',
};

const ROLE_SCOPE = {
    ALL: 'all',
    COMPANY: 'company',
    BRANCH: 'branch',
    DEPARTMENT: 'department',
};

const USER_STATUS = {
    ALL: 'all',
    ACTIVE: 'active',
    UNACTIVE: 'unactive',
};

const TENANT_STATUS = {
    ALL: 'all',
    ACTIVE: 'active',
    UNACTIVE: 'unactive',
    TRUE: 1,
    FALSE: 0,
};

const PROCESS_STAGE_STATUS = {
    PROCESSING: 'processing',
    COMPLETED: 'completed',
    PENDING: 'pending',
    CANCEL: 'cancel',
};

export {
    WORKFLOWS,
    SAVE_JOB_STATUS,
    TAB_JOB_DETAIL,
    PERMISSION_STATUS,
    ROLE_STATUS,
    ROLE_SCOPE,
    USER_STATUS,
    TENANT_STATUS,
    PROCESS_STAGE_STATUS,
};