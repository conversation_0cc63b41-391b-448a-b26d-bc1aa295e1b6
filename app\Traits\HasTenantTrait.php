<?php

namespace App\Traits;

use App\Models\Scopes\TenantScope;

trait HasTenantTrait
{
    public static function bootHasTenantTrait()
    {
        // Tự động áp dụng scope lọc theo đơn vị
        static::addGlobalScope(new TenantScope);

        // Tự động gán tanent_id khi tạo mới một bản ghi
        static::creating(function ($model) {
            if (app()->has('tenant')) {
                $model->tenant_id = app('tenant')->id;
            }
        });
    }
}