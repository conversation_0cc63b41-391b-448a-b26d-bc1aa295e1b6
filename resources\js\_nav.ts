import i18n from './lang/i18n';

const menuItems = [
	{
		component: 'CNavItem',
		name: 'sidebar.home',
		to: '/home',
		icon: 'home',
		// permissions: ['view-home'], // Y<PERSON><PERSON> cầu quyền cụ thể
		// requireAllPermissions: false, // Mặc định là false, nếu true thì yêu cầu tất cả quyền
		// roles: ['Admin'] // Yêu cầu một trong các vai trò này
	},
	{
		component: 'CNavItem',
		name: 'sidebar.dashboard',
		to: '/dashboard',
		icon: 'bar_chart_4_bars',
		permissions: ['VIEW-DASHBOARD'], // Yêu cầu một trong các quyền này
		requireAllPermissions: false,
	},
	{
		component: 'CNavTitle',
		name: 'sidebar.workplace.title',
	},
	{
		component: 'CNavItem',
		name: 'sidebar.workplace.workflow',
		to: '/workflow',
		icon: 'rebase',
		permissions: ['VIEW-WORKFLOW'],
		requireAllPermissions: false,
	},
	{
		component: 'CNavItem',
		name: 'sidebar.workplace.job',
		to: '/job',
		icon: 'work',
		permissions: ['VIEW-JOB'],
		requireAllPermissions: false,
	},
	{
		component: 'CNavTitle',
		name: 'sidebar.setting_system',
		permissions: ['ADMIN-SETTING'],
		requireAllPermissions: false,
	},
	{
		component: 'CNavGroup',
		name: 'sidebar.system.title',
		icon: 'settings_applications',
		permissions: ['ADMIN-SETTING'],
		requireAllPermissions: false,
		items: [
			{
				component: 'CNavItem',
				name: 'sidebar.system.role',
				to: '/admin-role',
				permissions: ['ADMIN-SETTING'],
				requireAllPermissions: false,
			},
			{
				component: 'CNavItem',
				name: 'sidebar.system.user',
				to: '/admin-user',
				permissions: ['ADMIN-SETTING'],
				requireAllPermissions: false,
			},
			{
				component: 'CNavItem',
				name: 'sidebar.system.department',
				to: '/admin-department',
				permissions: ['ADMIN-SETTING'],
				requireAllPermissions: false,
			},
			{
				component: 'CNavItem',
				name: 'sidebar.system.position',
				to: '/admin-position',
				permissions: ['ADMIN-SETTING'],
				requireAllPermissions: false,
			},
			{
				component: 'CNavItem',
				name: 'sidebar.system.job_positions',
				to: '/admin-job-position',
				permissions: ['ADMIN-SETTING'],
				requireAllPermissions: false,
			},
			{
				component: 'CNavItem',
				name: 'sidebar.system.cost_center',
				to: '/admin-cost-center',
				permissions: ['ADMIN-SETTING'],
				requireAllPermissions: false,
			},
			{
				component: 'CNavItem',
				name: 'sidebar.setting.email',
				to: '/admin-email',
				permissions: ['ADMIN-SETTING'],
				requireAllPermissions: false,
			},
			{
				component: 'CNavItem',
				name: 'sidebar.setting.parameter',
				to: '/admin-parameter',
				permissions: ['ADMIN-SETTING'],
				requireAllPermissions: false,
			},
		],
	},
	{
		component: 'CNavTitle',
		name: 'sidebar.setup_system',
		permissions: ['SUPER-ADMIN-SETTING'],
		requireAllPermissions: false,
	},
	{
		component: 'CNavGroup',
		name: 'sidebar.setting.title',
		icon: 'settings',
		permissions: ['SUPER-ADMIN-SETTING'],
		requireAllPermissions: false,
		items: [
			{
				component: 'CNavItem',
				name: 'sidebar.setting.permission',
				to: '/admin-permission',
				permissions: ['SUPER-ADMIN-SETTING'],
				requireAllPermissions: false,
			},
			{
				component: 'CNavItem',
				name: 'sidebar.setting.tenant',
				to: '/admin-tenant',
				permissions: ['SUPER-ADMIN-SETTING'],
				requireAllPermissions: false,
			}
		],
	},
	
];

// Hàm để lấy các mục menu đã được dịch
export function getLocalizedMenuItems() {
    // Hàm đệ quy để dịch các mục menu
    const localizeItems = (items: any) => {
        return items.map((item: any) => {
            const localizedItem = {
                ...item,
                name: i18n.global.t(item.name),
            };

            // Nếu có các mục lồng nhau, dịch chúng đệ quy
            if (item.items) {
                localizedItem.items = localizeItems(item.items);
            }

            return localizedItem;
        });
    };

    // Dịch tất cả các mục menu
    return localizeItems(menuItems);
};

// Export danh sách menu mặc định (để tương thích ngược)
export default getLocalizedMenuItems();


