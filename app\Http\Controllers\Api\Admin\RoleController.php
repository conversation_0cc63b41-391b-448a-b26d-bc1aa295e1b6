<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Repositories\Role\RoleRepositoryInterface;
use App\Enums\RoleIsHidden;
use Illuminate\Support\Facades\DB;
use App\Http\Requests\RoleRequest;
use Illuminate\Support\Facades\Auth;

class RoleController extends Controller
{
    private $roleRepository;

    public function __construct(
        RoleRepositoryInterface $roleRepository,
    )
    {
        $this->roleRepository = $roleRepository;
    }

    public function getAllRoleOptions()
    {
        $roles = $this->roleRepository->getAllRoleOptions();

        return response()->json($roles);
    }
    
    public function getAllRoles(Request $request)
    {   
        $dataSearch = $request->all();
        $result = $this->roleRepository->getAllRoles($dataSearch);
        if (isset($result)) {
            
            return response()->json([
                'status' => 'success',
                'counts' => $result['counts'],
                'roles' => $result['roles'],
            ], 200);
        } else {

            return response()->json(['status' => 'not_data'], 200);
        }
    }

    public function storeRole(RoleRequest $request)
    {
        try {
            $validatedData = $request->validated();

            DB::beginTransaction();

            $role_data = [
                'name' => $validatedData['name'], 
                'is_hidden' => RoleIsHidden::SHOW->value, 
                'create_by' => Auth::id(), 
            ];
            $role = $this->roleRepository->create($role_data);

            // If permissions are provided, prepare and sync them with the role
            if (!empty($validatedData['permissions'])) {
                $permissionsToSync = [];
                foreach ($validatedData['permissions'] as $permissionData) {
                    $permissionsToSync[$permissionData['id']] = [
                        'scope' => $permissionData['scope']
                    ];
                }
                $role->permissions()->sync($permissionsToSync);
            }
            DB::commit();
            // Load the permissions with pivot data for the response
            $role->load('permissions');

            return response()->json([
                'status' => 'success',
                'data' => $role,
            ], 201);
        } catch (\Illuminate\Validation\ValidationException $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'error',
                'message' => __('error.422'),
                'errors' => $e->errors(),
            ], 422);
        } catch (\Throwable $th) {
            
            return response()->json([
                'status' => 'error',
                'message' => __('error.500'),
                'error_details' => $th->getMessage(),
            ], 500);
        }
    }
}
